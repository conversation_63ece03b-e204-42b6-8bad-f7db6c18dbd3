package logic

import (
	"activitysrv/internal/model"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// ActivityMetricsProcessor 活动指标处理器接口
// 不同活动类型可以实现不同的指标处理逻辑
type ActivityMetricsProcessor interface {
	// ProcessMetrics 处理活动指标更新
	// userData: 用户活动数据
	// event: 事件数据
	// activityCfg: 活动配置
	ProcessMetrics(userData *model.UserActivityData, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error
	
	// GetProcessorType 获取处理器类型标识
	GetProcessorType() string
}
