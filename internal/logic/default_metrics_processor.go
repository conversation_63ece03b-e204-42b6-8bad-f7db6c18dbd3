package logic

import (
	"activitysrv/internal/model"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
)

// DefaultMetricsProcessor 默认活动指标处理器
// 实现通用的指标处理逻辑，适用于大多数活动类型
type DefaultMetricsProcessor struct{}

// ProcessMetrics 处理活动指标更新 - 默认实现
func (p *DefaultMetricsProcessor) ProcessMetrics(userData *model.UserActivityData, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error {
	eventData := event.GetIntData()
	if eventData == nil {
		return fmt.Errorf("event data is empty")
	}

	for metricType, eventValue := range eventData {
		// 检查该指标是否与活动相关
		if !isMetricRelevantToActivity(metricType, activityCfg) {
			continue
		}

		// 获取当前指标值
		currentValue := userData.Metrics[metricType]

		// 根据活动配置的更新方式计算新值
		newValue := operate.SumAddVal(commonPB.SUM_ADD(activityCfg.Update), currentValue, eventValue)

		// 更新指标
		userData.Metrics[metricType] = newValue
	}

	return nil
}

// GetProcessorType 获取处理器类型标识
func (p *DefaultMetricsProcessor) GetProcessorType() string {
	return "default"
}

// isMetricRelevantToActivity 检查指标是否与活动相关
func isMetricRelevantToActivity(metricType int32, activityCfg *cmodel.Activity) bool {
	if metricType == activityCfg.Target {
		return true
	}
	return false
}
