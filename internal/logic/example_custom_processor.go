package logic

import (
	"activitysrv/internal/model"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"github.com/sirupsen/logrus"
)

// ExampleCustomMetricsProcessor 示例自定义活动指标处理器
// 展示如何为特定活动类型实现自定义的指标处理逻辑
type ExampleCustomMetricsProcessor struct{}

// ProcessMetrics 处理活动指标更新 - 自定义实现示例
// 这个示例展示了如何实现更复杂的指标处理逻辑：
// 1. 支持多个指标类型
// 2. 不同指标使用不同的计算方式
// 3. 添加额外的业务逻辑验证
func (p *ExampleCustomMetricsProcessor) ProcessMetrics(userData *model.UserActivityData, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error {
	eventData := event.GetIntData()
	if eventData == nil {
		return fmt.Errorf("event data is empty")
	}

	logrus.Debugf("custom processor handling activity %d with event data: %+v", activityCfg.Id, eventData)

	for metricType, eventValue := range eventData {
		// 自定义的指标相关性检查
		if !p.isMetricRelevantToActivity(metricType, activityCfg) {
			continue
		}

		// 获取当前指标值
		currentValue := userData.Metrics[metricType]

		// 自定义的指标计算逻辑
		newValue, err := p.calculateNewMetricValue(metricType, currentValue, eventValue, activityCfg)
		if err != nil {
			logrus.Errorf("failed to calculate new metric value: metricType=%d, err=%v", metricType, err)
			continue
		}

		// 更新指标
		userData.Metrics[metricType] = newValue
		
		logrus.Debugf("updated metric: type=%d, old=%d, new=%d", metricType, currentValue, newValue)
	}

	return nil
}

// GetProcessorType 获取处理器类型标识
func (p *ExampleCustomMetricsProcessor) GetProcessorType() string {
	return "example_custom"
}

// isMetricRelevantToActivity 自定义的指标相关性检查
// 示例：支持多个指标类型
func (p *ExampleCustomMetricsProcessor) isMetricRelevantToActivity(metricType int32, activityCfg *cmodel.Activity) bool {
	// 主要指标
	if metricType == activityCfg.Target {
		return true
	}
	
	// 示例：支持额外的指标类型
	// 这里可以根据具体业务需求添加更多指标类型的支持
	extraMetrics := []int32{1001, 1002, 1003} // 示例指标类型
	for _, extraMetric := range extraMetrics {
		if metricType == extraMetric {
			return true
		}
	}
	
	return false
}

// calculateNewMetricValue 自定义的指标计算逻辑
// 示例：不同指标类型使用不同的计算方式
func (p *ExampleCustomMetricsProcessor) calculateNewMetricValue(metricType int32, currentValue, eventValue int64, activityCfg *cmodel.Activity) (int64, error) {
	// 主要指标使用配置的更新方式
	if metricType == activityCfg.Target {
		return operate.SumAddVal(commonPB.SUM_ADD(activityCfg.Update), currentValue, eventValue), nil
	}
	
	// 示例：不同指标类型的自定义计算逻辑
	switch metricType {
	case 1001: // 示例：累加指标
		return currentValue + eventValue, nil
	case 1002: // 示例：最大值指标
		if eventValue > currentValue {
			return eventValue, nil
		}
		return currentValue, nil
	case 1003: // 示例：计数指标（每次事件+1）
		return currentValue + 1, nil
	default:
		// 默认使用配置的更新方式
		return operate.SumAddVal(commonPB.SUM_ADD(activityCfg.Update), currentValue, eventValue), nil
	}
}

// 示例：如何注册自定义处理器
// 这个函数展示了如何在系统启动时注册自定义处理器
func RegisterExampleCustomProcessor() {
	registry := GetProcessorRegistry()
	processor := &ExampleCustomMetricsProcessor{}
	
	// 为特定活动ID注册处理器
	registry.RegisterActivityProcessor(10001, processor) // 假设活动ID为10001
	
	// 或者为特定活动类型注册处理器
	registry.RegisterTypeProcessor("special_activity", processor)
	
	logrus.Infof("registered custom metrics processor: %s", processor.GetProcessorType())
}
