package logic

import (
	"github.com/sirupsen/logrus"
)

// InitActivityProcessors 初始化活动指标处理器
// 在系统启动时调用，注册各种自定义处理器
func InitActivityProcessors() {
	logrus.Info("initializing activity metrics processors...")
	
	// 注册示例自定义处理器
	// 在实际使用中，可以根据需要注册不同的处理器
	RegisterExampleCustomProcessor()
	
	// 这里可以添加更多处理器的注册
	// 例如：
	// RegisterSpecialActivityProcessor()
	// RegisterEventBasedProcessor()
	// RegisterTimeBasedProcessor()
	
	logrus.Info("activity metrics processors initialized successfully")
}

// 使用说明：
// 1. 在 main.go 或适当的初始化位置调用 InitActivityProcessors()
// 2. 为不同活动类型创建自定义处理器
// 3. 使用 RegisterActivityProcessor 或 RegisterTypeProcessor 注册处理器
// 4. 系统会自动根据活动ID或类型选择合适的处理器
