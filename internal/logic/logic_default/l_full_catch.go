package logic_default

import (
	"activitysrv/internal/model"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// FullCatchProcessor 爆护之路
type FullCatchProcessor struct {
	DefaultMetricsProcessor
}

func (p *FullCatchProcessor) ProcessMetrics(userData *model.UserActivityData, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error {
	return p.DefaultMetricsProcessor.ProcessMetrics(userData, event, activityCfg)
}
