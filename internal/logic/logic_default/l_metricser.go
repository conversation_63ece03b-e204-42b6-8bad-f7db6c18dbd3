package logic_default

import (
	"activitysrv/internal/model"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
)

// GetActivityMetricsProcessor 获取活动指标处理器
func GetActivityMetricsProcessor(activityId int64) ActivityMetricsProcessor {
	switch activityId {
	case 10001:
		return &FullCatchProcessor{}
	default:
		return &DefaultMetricsProcessor{}
	}
}

// ActivityMetricsProcessor 活动指标处理器接口
type ActivityMetricsProcessor interface {
	ProcessMetrics(userData *model.UserActivityData, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error
}

// DefaultMetricsProcessor 默认指标处理器
type DefaultMetricsProcessor struct{}

// ProcessMetrics 默认的指标处理逻辑
func (p *DefaultMetricsProcessor) ProcessMetrics(userData *model.UserActivityData, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error {
	eventData := event.GetIntData()
	if eventData == nil {
		return fmt.Errorf("event data is empty")
	}

	for metricType, eventValue := range eventData {
		// 检查该指标是否与活动相关
		if !isMetricRelevantToActivity(metricType, activityCfg) {
			continue
		}

		// 获取当前指标值
		currentValue := userData.Metrics[metricType]

		// 根据活动配置的更新方式计算新值
		newValue := operate.SumAddVal(commonPB.SUM_ADD(activityCfg.Update), currentValue, eventValue)

		// 更新指标
		userData.Metrics[metricType] = newValue
	}

	return nil
}
