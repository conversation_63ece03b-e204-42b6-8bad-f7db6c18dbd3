package logic

import (
	"sync"
)

// ProcessorRegistry 活动指标处理器注册表
type ProcessorRegistry struct {
	// 按活动ID注册的处理器
	activityProcessors map[int64]ActivityMetricsProcessor
	// 按活动类型注册的处理器
	typeProcessors map[string]ActivityMetricsProcessor
	// 默认处理器
	defaultProcessor ActivityMetricsProcessor
	mutex            sync.RWMutex
}

var (
	registry *ProcessorRegistry
	once     sync.Once
)

// GetProcessorRegistry 获取处理器注册表单例
func GetProcessorRegistry() *ProcessorRegistry {
	once.Do(func() {
		registry = &ProcessorRegistry{
			activityProcessors: make(map[int64]ActivityMetricsProcessor),
			typeProcessors:     make(map[string]ActivityMetricsProcessor),
			defaultProcessor:   &DefaultMetricsProcessor{},
		}
	})
	return registry
}

// RegisterActivityProcessor 为特定活动ID注册处理器
func (r *ProcessorRegistry) RegisterActivityProcessor(activityId int64, processor ActivityMetricsProcessor) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.activityProcessors[activityId] = processor
}

// RegisterTypeProcessor 为特定活动类型注册处理器
func (r *ProcessorRegistry) RegisterTypeProcessor(activityType string, processor ActivityMetricsProcessor) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.typeProcessors[activityType] = processor
}

// GetProcessor 获取活动指标处理器
// 优先级：活动ID > 活动类型 > 默认处理器
func (r *ProcessorRegistry) GetProcessor(activityId int64, activityType string) ActivityMetricsProcessor {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// 1. 优先查找按活动ID注册的处理器
	if processor, exists := r.activityProcessors[activityId]; exists {
		return processor
	}

	// 2. 查找按活动类型注册的处理器
	if processor, exists := r.typeProcessors[activityType]; exists {
		return processor
	}

	// 3. 返回默认处理器
	return r.defaultProcessor
}

// SetDefaultProcessor 设置默认处理器
func (r *ProcessorRegistry) SetDefaultProcessor(processor ActivityMetricsProcessor) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.defaultProcessor = processor
}

// GetActivityMetricsProcessor 获取活动指标处理器的便捷函数
// 目前只根据活动ID获取，后续可以扩展支持活动类型
func GetActivityMetricsProcessor(activityId int64) ActivityMetricsProcessor {
	registry := GetProcessorRegistry()
	// 暂时使用空字符串作为活动类型，后续可以从配置中获取
	return registry.GetProcessor(activityId, "")
}
